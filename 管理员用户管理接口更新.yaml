openapi: 3.0.3
info:
  title: 管理员用户管理接口
  description: 助教系统管理员用户管理相关接口文档
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

tags:
  - name: admin-user-management
    description: 管理员用户管理

paths:
  /admin/users:
    get:
      tags:
        - admin-user-management
      summary: 分页查询用户列表
      description: 管理员分页查询用户列表，支持按角色、学校、姓名筛选
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: 页码，默认1
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: size
          in: query
          description: 每页数量，默认10
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: schoolId
          in: query
          description: 学校ID筛选
          schema:
            type: integer
        - name: role
          in: query
          description: 角色筛选
          schema:
            type: string
            enum: [super_admin, principal, teacher]
        - name: realName
          in: query
          description: 真实姓名模糊搜索
          schema:
            type: string
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PageResult'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - admin-user-management
      summary: 创建用户
      description: 管理员创建新用户
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/users/{userId}:
    get:
      tags:
        - admin-user-management
      summary: 根据ID查询用户详情
      description: 管理员根据用户ID查询用户详细信息
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags:
        - admin-user-management
      summary: 更新用户信息
      description: 管理员更新用户信息
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - admin-user-management
      summary: 删除用户
      description: 管理员删除用户
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/users/{userId}/password:
    put:
      tags:
        - admin-user-management
      summary: 重置用户密码
      description: 管理员重置用户密码
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: 重置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/users/{userId}/status:
    put:
      tags:
        - admin-user-management
      summary: 更新用户状态
      description: 管理员更新用户状态（启用/禁用）
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
        - name: status
          in: query
          required: true
          description: 用户状态
          schema:
            type: string
            enum: [active, inactive]
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/schools/all:
    get:
      tags:
        - admin-user-management
      summary: 获取所有学校列表
      description: 获取所有学校列表，用于下拉选择
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/School'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳

    PageResult:
      type: object
      properties:
        total:
          type: integer
          description: 总记录数
          example: 100
        page:
          type: integer
          description: 当前页码
          example: 1
        size:
          type: integer
          description: 每页数量
          example: 10
        list:
          type: array
          description: 数据列表
          items:
            $ref: '#/components/schemas/UserInfo'

    UserInfo:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        username:
          type: string
          description: 用户名
          example: "zhangsan"
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        phone:
          type: string
          description: 手机号
          example: "13800138000"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          description: 用户角色
          example: "teacher"
        schoolId:
          type: integer
          format: int64
          description: 学校ID
          example: 201
        schoolName:
          type: string
          description: 学校名称
          example: "示例小学"
        gender:
          type: string
          enum: [M, F]
          description: 性别，M-男，F-女
          example: "M"
        hireDate:
          type: string
          format: date
          description: 入职时间
          example: "2023-01-15"
        subject:
          type: string
          description: 主教科目（教师角色使用）
          example: "数学"
        experience:
          type: integer
          description: 教学经验年数（教师角色使用）
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学教师"
        status:
          type: string
          enum: [active, inactive]
          description: 账号状态
          example: "active"
        lastLoginAt:
          type: string
          format: date-time
          description: 最后登录时间
          example: "2024-01-15T14:20:00Z"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2023-01-15T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15T14:20:00Z"

    CreateUserRequest:
      type: object
      required:
        - username
        - password
        - realName
        - role
        - schoolId
      properties:
        username:
          type: string
          pattern: '^[a-zA-Z0-9_]{4,20}$'
          description: 用户名，只能包含字母、数字、下划线，长度4-20位
          example: "zhangsan"
        password:
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$'
          description: 密码，必须包含大小写字母和数字，长度6-20位
          example: "Password123"
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        role:
          type: string
          enum: [super_admin, principal, teacher]
          description: 用户角色
          example: "teacher"
        schoolId:
          type: integer
          format: int64
          description: 学校ID
          example: 201
        phone:
          type: string
          pattern: '^1[3-9]\d{9}$'
          description: 手机号
          example: "13800138000"
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        gender:
          type: string
          enum: [M, F]
          description: 性别，M-男，F-女
          example: "M"
        hireDate:
          type: string
          format: date
          description: 入职时间
          example: "2023-01-15"
        subject:
          type: string
          description: 主教科目（教师角色使用）
          example: "数学"
        experience:
          type: integer
          minimum: 0
          maximum: 50
          description: 教学经验年数（教师角色使用）
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学教师"

    UpdateUserRequest:
      type: object
      properties:
        realName:
          type: string
          description: 真实姓名
          example: "张三"
        phone:
          type: string
          pattern: '^1[3-9]\d{9}$'
          description: 手机号
          example: "13800138000"
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        gender:
          type: string
          enum: [M, F]
          description: 性别，M-男，F-女
          example: "M"
        schoolId:
          type: integer
          format: int64
          description: 学校ID
          example: 201
        hireDate:
          type: string
          format: date
          description: 入职时间
          example: "2023-01-15"
        subject:
          type: string
          description: 主教科目（教师角色使用）
          example: "数学"
        experience:
          type: integer
          minimum: 0
          maximum: 50
          description: 教学经验年数（教师角色使用）
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学教师"
        status:
          type: string
          enum: [active, inactive]
          description: 账号状态
          example: "active"

    ResetPasswordRequest:
      type: object
      required:
        - newPassword
      properties:
        newPassword:
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$'
          description: 新密码，必须包含大小写字母和数字，长度6-20位
          example: "NewPassword123"

    School:
      type: object
      properties:
        schoolId:
          type: integer
          format: int64
          description: 学校ID
          example: 201
        name:
          type: string
          description: 学校名称
          example: "示例小学"
        address:
          type: string
          description: 学校地址
          example: "北京市朝阳区示例街道123号"
        adminQuota:
          type: integer
          description: 管理员配额
          example: 3
        status:
          type: string
          enum: [active, inactive]
          description: 学校状态
          example: "active"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2023-01-15T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15T14:20:00Z"

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 400
        message:
          type: string
          description: 错误消息
          example: "参数错误"
        timestamp:
          type: string
          format: date-time
          description: 错误时间戳

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: 未授权，需要登录
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

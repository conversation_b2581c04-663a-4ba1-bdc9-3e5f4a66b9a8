import { request } from '@/utils/request'
import type { ApiResponse } from '@/types'

// 用户管理相关类型定义
export interface UserListParams {
  page?: number
  size?: number
  schoolId?: number
  role?: string
  realName?: string
}

export interface CreateUserData {
  username: string
  password: string
  realName: string
  role: string
  schoolId: number
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
}

export interface UpdateUserData {
  realName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  schoolId?: number
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
  status?: 'active' | 'inactive'
}

export interface UserInfo {
  userId: number
  username: string
  realName: string
  role: string
  schoolId?: number
  schoolName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
  status?: string
  lastLoginAt?: string
  createdAt?: string
  updatedAt?: string
}

export interface PageResult<T> {
  total: number
  page: number
  size: number
  list: T[]
}

export interface School {
  schoolId: number
  name: string
  address?: string
  adminQuota?: number
  status?: string
  createdAt?: string
  updatedAt?: string
}

/**
 * 管理员用户管理API
 */
export const adminApi = {
  /**
   * 获取用户列表
   */
  getUserList(params: UserListParams): Promise<ApiResponse<PageResult<UserInfo>>> {
    return request.get('/admin/users', params)
  },

  /**
   * 根据ID获取用户详情
   */
  getUserById(userId: number): Promise<ApiResponse<UserInfo>> {
    return request.get(`/admin/users/${userId}`)
  },

  /**
   * 创建用户
   */
  createUser(data: CreateUserData): Promise<ApiResponse<UserInfo>> {
    return request.post('/admin/users', data)
  },

  /**
   * 更新用户信息
   */
  updateUser(userId: number, data: UpdateUserData): Promise<ApiResponse<UserInfo>> {
    return request.put(`/admin/users/${userId}`, data)
  },

  /**
   * 删除用户
   */
  deleteUser(userId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/users/${userId}`)
  },

  /**
   * 重置用户密码
   */
  resetPassword(userId: number, newPassword: string): Promise<ApiResponse<void>> {
    return request.put(`/admin/users/${userId}/password`, { newPassword })
  },

  /**
   * 更新用户状态
   */
  updateUserStatus(userId: number, status: 'active' | 'inactive'): Promise<ApiResponse<void>> {
    return request.put(`/admin/users/${userId}/status?status=${status}`)
  },

  /**
   * 获取所有学校列表（用于下拉选择）
   */
  getAllSchools(): Promise<ApiResponse<School[]>> {
    return request.get('/admin/schools/all')
  }
}
